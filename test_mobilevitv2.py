#!/usr/bin/env python3
"""
Test script for MobileViTV2 models integration
验证MobileViTV2模型是否正确集成到项目中
"""

import torch
import torch.nn as nn
import sys
import os

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import network.modeling as modeling

def test_model_creation():
    """测试模型创建"""
    print("🧪 Testing MobileViTV2 model creation...")
    
    models_to_test = [
        ('mobilevitv2_1_0', 'MobileViTV2-1.0'),
        ('mobilevitv2_0_75', 'MobileViTV2-0.75'),
        ('mobilevitv2_0_5', 'MobileViTV2-0.5')
    ]
    
    for model_name, display_name in models_to_test:
        try:
            print(f"\n📋 Testing {display_name}...")
            
            # Test if model function exists
            if not hasattr(modeling, model_name):
                print(f"❌ Model function {model_name} not found in modeling.py")
                continue
            
            # Create model
            model_func = getattr(modeling, model_name)
            model = model_func(num_classes=2, pretrained_backbone=False)  # Use False to avoid downloading
            
            print(f"✅ {display_name} created successfully")
            print(f"   Model type: {type(model).__name__}")
            
            # Test model attributes
            if hasattr(model, 'backbone'):
                print(f"   Has backbone: ✅")
            else:
                print(f"   Has backbone: ❌")
                
            if hasattr(model, 'classifier'):
                print(f"   Has classifier: ✅")
            else:
                print(f"   Has classifier: ❌")
            
        except Exception as e:
            print(f"❌ Failed to create {display_name}: {str(e)}")

def test_model_forward():
    """测试模型前向传播"""
    print("\n🚀 Testing MobileViTV2 forward pass...")
    
    try:
        # Create a small test model
        print("📋 Creating MobileViTV2-0.5 for forward test...")
        model = modeling.mobilevitv2_0_5(num_classes=2, pretrained_backbone=False)
        model.eval()
        
        # Create test input
        batch_size = 2
        channels = 3
        height = 256  # Smaller size for testing
        width = 256
        
        print(f"📋 Creating test input: {batch_size}x{channels}x{height}x{width}")
        test_input = torch.randn(batch_size, channels, height, width)
        
        # Forward pass
        print("📋 Running forward pass...")
        with torch.no_grad():
            output = model(test_input)
        
        print(f"✅ Forward pass successful!")
        print(f"   Input shape: {test_input.shape}")
        print(f"   Output shape: {output.shape}")
        print(f"   Expected output shape: {(batch_size, 2, height, width)}")
        
        # Check output shape
        expected_shape = (batch_size, 2, height, width)
        if output.shape == expected_shape:
            print(f"✅ Output shape is correct!")
        else:
            print(f"❌ Output shape mismatch. Expected: {expected_shape}, Got: {output.shape}")
            
    except Exception as e:
        print(f"❌ Forward pass failed: {str(e)}")
        import traceback
        traceback.print_exc()

def test_model_parameters():
    """测试模型参数数量"""
    print("\n📊 Testing MobileViTV2 model parameters...")
    
    models_to_test = [
        ('mobilevitv2_1_0', 'MobileViTV2-1.0', '~4.9M'),
        ('mobilevitv2_0_75', 'MobileViTV2-0.75', '~3.7M'),
        ('mobilevitv2_0_5', 'MobileViTV2-0.5', '~2.3M')
    ]
    
    for model_name, display_name, expected_params in models_to_test:
        try:
            print(f"\n📋 Analyzing {display_name}...")
            
            model_func = getattr(modeling, model_name)
            model = model_func(num_classes=2, pretrained_backbone=False)
            
            # Count parameters
            total_params = sum(p.numel() for p in model.parameters())
            trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
            
            print(f"✅ {display_name} parameter analysis:")
            print(f"   Total parameters: {total_params:,} ({total_params/1e6:.2f}M)")
            print(f"   Trainable parameters: {trainable_params:,} ({trainable_params/1e6:.2f}M)")
            print(f"   Expected: {expected_params}")
            
        except Exception as e:
            print(f"❌ Failed to analyze {display_name}: {str(e)}")

def test_model_compatibility():
    """测试模型与现有训练流程的兼容性"""
    print("\n🔧 Testing MobileViTV2 compatibility with training pipeline...")
    
    try:
        print("📋 Creating MobileViTV2-0.5...")
        model = modeling.mobilevitv2_0_5(num_classes=2, pretrained_backbone=False)
        
        # Test optimizer creation (similar to main.py)
        print("📋 Testing AdamW optimizer creation...")
        optimizer = torch.optim.AdamW(
            params=model.parameters(),
            lr=0.001 * 0.1,  # Reduced learning rate as in main.py
            weight_decay=1e-4
        )
        print("✅ AdamW optimizer created successfully")
        
        # Test gradient clipping
        print("📋 Testing gradient clipping...")
        test_input = torch.randn(1, 3, 256, 256)
        test_target = torch.randint(0, 2, (1, 256, 256))
        
        # Forward pass
        output = model(test_input)
        
        # Compute loss
        criterion = nn.CrossEntropyLoss()
        loss = criterion(output, test_target)
        
        # Backward pass
        loss.backward()
        
        # Gradient clipping
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        print("✅ Gradient clipping successful")
        
        # Optimizer step
        optimizer.step()
        optimizer.zero_grad()
        print("✅ Optimizer step successful")
        
        print("✅ MobileViTV2 is fully compatible with training pipeline!")
        
    except Exception as e:
        print(f"❌ Compatibility test failed: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("🎯 MobileViTV2 Integration Test")
    print("=" * 50)
    
    # Test model creation
    test_model_creation()
    
    # Test forward pass
    test_model_forward()
    
    # Test parameter counts
    test_model_parameters()
    
    # Test compatibility
    test_model_compatibility()
    
    print("\n" + "=" * 50)
    print("🎉 MobileViTV2 integration test completed!")
    print("\n💡 Usage examples:")
    print("   Training: python main.py --model mobilevitv2_0_5 --dataset Jiayu --num_classes 2 --batch_size 16")
    print("   Evaluation: python main.py --model mobilevitv2_0_5 --dataset Jiayu --num_classes 2 --test_only --ckpt your_checkpoint.pth")

if __name__ == "__main__":
    main()
