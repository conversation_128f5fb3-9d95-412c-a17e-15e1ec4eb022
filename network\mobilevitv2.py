import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import MobileViTV2ForSemanticSegmentation, MobileViTV2Config, AutoImageProcessor

class MobileViTV2Wrapper(nn.Module):
    """
    MobileViTV2模型的封装类，用于集成到DeepLabV3Plus-Pytorch项目中
    
    MobileViTV2是一种轻量级的视觉Transformer模型，专为移动设备设计，
    在保持高精度的同时大幅减少参数量和计算复杂度。
    """
    def __init__(self, model_name="apple/mobilevitv2-1.0-imagenet1k-256", 
                 num_classes=21, pretrained_backbone=True, output_stride=None):
        """
        初始化MobileViTV2模型
        
        Args:
            model_name (str): 模型名称，如"apple/mobilevitv2-1.0-imagenet1k-256"
            num_classes (int): 分类类别数量
            pretrained_backbone (bool): 是否使用预训练的骨干网络
            output_stride (int): 输出步长，MobileViTV2不使用此参数，但为了与DeepLabV3+接口兼容而保留
        """
        super(MobileViTV2Wrapper, self).__init__()
        
        self.model_name = model_name
        self.num_classes = num_classes
        
        # 添加图像处理器，用于模型输入预处理
        try:
            self.image_processor = AutoImageProcessor.from_pretrained(self.model_name)
            # 获取图像处理器的归一化参数
            self.mean = torch.tensor(self.image_processor.image_mean).view(1, 3, 1, 1)
            self.std = torch.tensor(self.image_processor.image_std).view(1, 3, 1, 1)
        except:
            # 如果无法获取预训练的处理器，使用默认值
            self.mean = torch.tensor([0.485, 0.456, 0.406]).view(1, 3, 1, 1)
            self.std = torch.tensor([0.229, 0.224, 0.225]).view(1, 3, 1, 1)
            self.image_processor = None
        
        if pretrained_backbone:
            # 使用预训练模型，并根据类别数量调整输出层
            try:
                # 尝试加载语义分割版本
                self.model = MobileViTV2ForSemanticSegmentation.from_pretrained(
                    self.model_name,
                    num_labels=num_classes,
                    ignore_mismatched_sizes=True
                )
            except:
                # 如果没有语义分割版本，从分类模型创建
                config = MobileViTV2Config.from_pretrained(self.model_name)
                config.num_labels = num_classes
                # 添加语义分割头
                config.use_auxiliary_head = True
                self.model = MobileViTV2ForSemanticSegmentation(config)
                print(f"Warning: Created MobileViTV2 from scratch as no pretrained segmentation model found for {self.model_name}")
        else:
            # 从头开始训练，创建配置并初始化模型
            config = MobileViTV2Config()
            config.num_labels = num_classes
            config.use_auxiliary_head = True
            self.model = MobileViTV2ForSemanticSegmentation(config)
        
        # 为了与DeepLabV3+接口兼容，添加backbone属性
        # 这个属性在main.py中被用于设置BN层的momentum
        self.backbone = self.model.mobilevitv2
        
        # 添加classifier属性，用于与DeepLabV3+接口兼容
        # 在main.py中优化器设置时会用到
        if hasattr(self.model, 'segmentation_head'):
            self.classifier = self.model.segmentation_head
        else:
            # 创建一个占位符
            self.classifier = nn.Sequential()
        
        # 记录设备信息，用于归一化参数的设备转换
        self.device = None
    
    def _update_device(self, x):
        """更新设备信息并移动归一化参数到正确的设备"""
        if self.device != x.device:
            self.device = x.device
            self.mean = self.mean.to(x.device)
            self.std = self.std.to(x.device)
    
    def _normalize_input(self, x):
        """
        对输入进行归一化处理
        
        Args:
            x (torch.Tensor): 输入图像张量，形状为 (B, C, H, W)，值范围 [0, 1]
            
        Returns:
            torch.Tensor: 归一化后的图像张量
        """
        self._update_device(x)
        return (x - self.mean) / self.std
    
    def forward(self, x):
        """
        前向传播
        
        Args:
            x (torch.Tensor): 输入图像张量，形状为 (B, C, H, W)
            
        Returns:
            torch.Tensor: 分割结果，形状为 (B, num_classes, H, W)
        """
        # 获取输入尺寸
        input_shape = x.shape[-2:]
        
        # 对输入进行归一化（如果需要）
        # 注意：如果输入已经是归一化的，可能需要调整
        if x.max() > 1.0:
            x = x / 255.0  # 假设输入是0-255范围
        
        # 应用ImageNet归一化
        x = self._normalize_input(x)
        
        # 通过模型进行前向传播
        outputs = self.model(pixel_values=x)
        
        # 获取logits
        logits = outputs.logits
        
        # 将输出调整到输入尺寸
        if logits.shape[-2:] != input_shape:
            logits = F.interpolate(
                logits, 
                size=input_shape, 
                mode='bilinear', 
                align_corners=False
            )
        
        return logits

def mobilevitv2_1_0(num_classes=21, output_stride=None, pretrained_backbone=True):
    """
    创建MobileViTV2-1.0模型
    
    Args:
        num_classes (int): 分类类别数量
        output_stride (int): 输出步长，MobileViTV2不使用此参数，但为了与DeepLabV3+接口兼容而保留
        pretrained_backbone (bool): 是否使用预训练的骨干网络
    
    Returns:
        MobileViTV2Wrapper: 封装的MobileViTV2模型
    """
    return MobileViTV2Wrapper(
        model_name="apple/mobilevitv2-1.0-imagenet1k-256", 
        num_classes=num_classes, 
        pretrained_backbone=pretrained_backbone, 
        output_stride=output_stride
    )

def mobilevitv2_0_75(num_classes=21, output_stride=None, pretrained_backbone=True):
    """
    创建MobileViTV2-0.75模型（更轻量）
    
    Args:
        num_classes (int): 分类类别数量
        output_stride (int): 输出步长，MobileViTV2不使用此参数，但为了与DeepLabV3+接口兼容而保留
        pretrained_backbone (bool): 是否使用预训练的骨干网络
    
    Returns:
        MobileViTV2Wrapper: 封装的MobileViTV2模型
    """
    return MobileViTV2Wrapper(
        model_name="apple/mobilevitv2-0.75-imagenet1k-256", 
        num_classes=num_classes, 
        pretrained_backbone=pretrained_backbone, 
        output_stride=output_stride
    )

def mobilevitv2_0_5(num_classes=21, output_stride=None, pretrained_backbone=True):
    """
    创建MobileViTV2-0.5模型（最轻量）
    
    Args:
        num_classes (int): 分类类别数量
        output_stride (int): 输出步长，MobileViTV2不使用此参数，但为了与DeepLabV3+接口兼容而保留
        pretrained_backbone (bool): 是否使用预训练的骨干网络
    
    Returns:
        MobileViTV2Wrapper: 封装的MobileViTV2模型
    """
    return MobileViTV2Wrapper(
        model_name="apple/mobilevitv2-0.5-imagenet1k-256", 
        num_classes=num_classes, 
        pretrained_backbone=pretrained_backbone, 
        output_stride=output_stride
    )
