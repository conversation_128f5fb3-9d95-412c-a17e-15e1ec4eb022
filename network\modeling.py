from .utils import IntermediateLayerGetter
from ._deeplab import DeepLabHead, DeepLabHeadV3Plus, DeepLabV3
from .backbone import (
    resnet,
    mobilenetv2,
    hrnetv2,
    xception
)

def _segm_hrnet(name, backbone_name, num_classes, pretrained_backbone):

    backbone = hrnetv2.__dict__[backbone_name](pretrained_backbone)
    # HRNetV2 config:
    # the final output channels is dependent on highest resolution channel config (c).
    # output of backbone will be the inplanes to assp:
    hrnet_channels = int(backbone_name.split('_')[-1])
    inplanes = sum([hrnet_channels * 2 ** i for i in range(4)])
    low_level_planes = 256 # all hrnet version channel output from bottleneck is the same
    aspp_dilate = [12, 24, 36] # If follow paper trend, can put [24, 48, 72].

    if name=='deeplabv3plus':
        return_layers = {'stage4': 'out', 'layer1': 'low_level'}
        classifier = DeepLabHeadV3Plus(inplanes, low_level_planes, num_classes, aspp_dilate)
    elif name=='deeplabv3':
        return_layers = {'stage4': 'out'}
        classifier = DeepLabHead(inplanes, num_classes, aspp_dilate)

    backbone = IntermediateLayerGetter(backbone, return_layers=return_layers, hrnet_flag=True)
    model = DeepLabV3(backbone, classifier)
    return model

def _segm_resnet(name, backbone_name, num_classes, output_stride, pretrained_backbone):

    if output_stride==8:
        replace_stride_with_dilation=[False, True, True]
        aspp_dilate = [12, 24, 36]
    else:
        replace_stride_with_dilation=[False, False, True]
        aspp_dilate = [6, 12, 18]

    backbone = resnet.__dict__[backbone_name](
        pretrained=pretrained_backbone,
        replace_stride_with_dilation=replace_stride_with_dilation)
    
    inplanes = 2048
    low_level_planes = 256

    if name=='deeplabv3plus':
        return_layers = {'layer4': 'out', 'layer1': 'low_level'}
        classifier = DeepLabHeadV3Plus(inplanes, low_level_planes, num_classes, aspp_dilate)
    elif name=='deeplabv3':
        return_layers = {'layer4': 'out'}
        classifier = DeepLabHead(inplanes , num_classes, aspp_dilate)
    backbone = IntermediateLayerGetter(backbone, return_layers=return_layers)

    model = DeepLabV3(backbone, classifier)
    return model


def _segm_xception(name, backbone_name, num_classes, output_stride, pretrained_backbone):
    if output_stride==8:
        replace_stride_with_dilation=[False, False, True, True]
        aspp_dilate = [12, 24, 36]
    else:
        replace_stride_with_dilation=[False, False, False, True]
        aspp_dilate = [6, 12, 18]
    
    backbone = xception.xception(pretrained= 'imagenet' if pretrained_backbone else False, replace_stride_with_dilation=replace_stride_with_dilation)
    
    inplanes = 2048
    low_level_planes = 128
    
    if name=='deeplabv3plus':
        return_layers = {'conv4': 'out', 'block1': 'low_level'}
        classifier = DeepLabHeadV3Plus(inplanes, low_level_planes, num_classes, aspp_dilate)
    elif name=='deeplabv3':
        return_layers = {'conv4': 'out'}
        classifier = DeepLabHead(inplanes , num_classes, aspp_dilate)
    backbone = IntermediateLayerGetter(backbone, return_layers=return_layers)
    model = DeepLabV3(backbone, classifier)
    return model


def _segm_mobilenet(name, backbone_name, num_classes, output_stride, pretrained_backbone):
    if output_stride==8:
        aspp_dilate = [12, 24, 36]
    else:
        aspp_dilate = [6, 12, 18]

    backbone = mobilenetv2.mobilenet_v2(pretrained=pretrained_backbone, output_stride=output_stride)
    
    # rename layers
    backbone.low_level_features = backbone.features[0:4]
    backbone.high_level_features = backbone.features[4:-1]
    backbone.features = None
    backbone.classifier = None

    inplanes = 320
    low_level_planes = 24
    
    if name=='deeplabv3plus':
        return_layers = {'high_level_features': 'out', 'low_level_features': 'low_level'}
        classifier = DeepLabHeadV3Plus(inplanes, low_level_planes, num_classes, aspp_dilate)
    elif name=='deeplabv3':
        return_layers = {'high_level_features': 'out'}
        classifier = DeepLabHead(inplanes , num_classes, aspp_dilate)
    backbone = IntermediateLayerGetter(backbone, return_layers=return_layers)

    model = DeepLabV3(backbone, classifier)
    return model

def _load_model(arch_type, backbone, num_classes, output_stride, pretrained_backbone):

    if backbone=='mobilenetv2':
        model = _segm_mobilenet(arch_type, backbone, num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)
    elif backbone.startswith('resnet'):
        model = _segm_resnet(arch_type, backbone, num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)
    elif backbone.startswith('hrnetv2'):
        model = _segm_hrnet(arch_type, backbone, num_classes, pretrained_backbone=pretrained_backbone)
    elif backbone=='xception':
        model = _segm_xception(arch_type, backbone, num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)
    else:
        raise NotImplementedError
    return model


# Deeplab v3
def deeplabv3_hrnetv2_48(num_classes=21, output_stride=4, pretrained_backbone=False): # no pretrained backbone yet
    return _load_model('deeplabv3', 'hrnetv2_48', output_stride, num_classes, pretrained_backbone=pretrained_backbone)

def deeplabv3_hrnetv2_32(num_classes=21, output_stride=4, pretrained_backbone=True):
    return _load_model('deeplabv3', 'hrnetv2_32', output_stride, num_classes, pretrained_backbone=pretrained_backbone)

def deeplabv3_resnet50(num_classes=21, output_stride=8, pretrained_backbone=True):
    """Constructs a DeepLabV3 model with a ResNet-50 backbone.

    Args:
        num_classes (int): number of classes.
        output_stride (int): output stride for deeplab.
        pretrained_backbone (bool): If True, use the pretrained backbone.
    """
    return _load_model('deeplabv3', 'resnet50', num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)

def deeplabv3_resnet101(num_classes=21, output_stride=8, pretrained_backbone=True):
    """Constructs a DeepLabV3 model with a ResNet-101 backbone.

    Args:
        num_classes (int): number of classes.
        output_stride (int): output stride for deeplab.
        pretrained_backbone (bool): If True, use the pretrained backbone.
    """
    return _load_model('deeplabv3', 'resnet101', num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)

def deeplabv3_mobilenet(num_classes=21, output_stride=8, pretrained_backbone=True, **kwargs):
    """Constructs a DeepLabV3 model with a MobileNetv2 backbone.

    Args:
        num_classes (int): number of classes.
        output_stride (int): output stride for deeplab.
        pretrained_backbone (bool): If True, use the pretrained backbone.
    """
    return _load_model('deeplabv3', 'mobilenetv2', num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)

def deeplabv3_xception(num_classes=21, output_stride=8, pretrained_backbone=True, **kwargs):
    """Constructs a DeepLabV3 model with a Xception backbone.

    Args:
        num_classes (int): number of classes.
        output_stride (int): output stride for deeplab.
        pretrained_backbone (bool): If True, use the pretrained backbone.
    """
    return _load_model('deeplabv3', 'xception', num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)


# Deeplab v3+
def deeplabv3plus_hrnetv2_48(num_classes=21, output_stride=4, pretrained_backbone=False): # no pretrained backbone yet
    return _load_model('deeplabv3plus', 'hrnetv2_48', num_classes, output_stride, pretrained_backbone=pretrained_backbone)

def deeplabv3plus_hrnetv2_32(num_classes=21, output_stride=4, pretrained_backbone=True):
    return _load_model('deeplabv3plus', 'hrnetv2_32', num_classes, output_stride, pretrained_backbone=pretrained_backbone)

def deeplabv3plus_resnet50(num_classes=21, output_stride=8, pretrained_backbone=True):
    """Constructs a DeepLabV3 model with a ResNet-50 backbone.

    Args:
        num_classes (int): number of classes.
        output_stride (int): output stride for deeplab.
        pretrained_backbone (bool): If True, use the pretrained backbone.
    """
    return _load_model('deeplabv3plus', 'resnet50', num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)


def deeplabv3plus_resnet101(num_classes=21, output_stride=8, pretrained_backbone=True):
    """Constructs a DeepLabV3+ model with a ResNet-101 backbone.

    Args:
        num_classes (int): number of classes.
        output_stride (int): output stride for deeplab.
        pretrained_backbone (bool): If True, use the pretrained backbone.
    """
    return _load_model('deeplabv3plus', 'resnet101', num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)


def deeplabv3plus_mobilenet(num_classes=21, output_stride=8, pretrained_backbone=True):
    """Constructs a DeepLabV3+ model with a MobileNetv2 backbone.

    Args:
        num_classes (int): number of classes.
        output_stride (int): output stride for deeplab.
        pretrained_backbone (bool): If True, use the pretrained backbone.
    """
    return _load_model('deeplabv3plus', 'mobilenetv2', num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)

def deeplabv3plus_xception(num_classes=21, output_stride=8, pretrained_backbone=True):
    """Constructs a DeepLabV3+ model with a Xception backbone.

    Args:
        num_classes (int): number of classes.
        output_stride (int): output stride for deeplab.
        pretrained_backbone (bool): If True, use the pretrained backbone.
    """
    return _load_model('deeplabv3plus', 'xception', num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)


# SegFormer models
def segformer_b0(num_classes=21, output_stride=None, pretrained_backbone=True):
    """Constructs a SegFormer-B0 model.

    Args:
        num_classes (int): number of classes.
        output_stride (int): not used for SegFormer, kept for API compatibility.
        pretrained_backbone (bool): If True, use the pretrained backbone.
    """
    from .segformer import segformer_b0 as segformer_b0_func
    return segformer_b0_func(num_classes=num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)

def segformer_b1(num_classes=21, output_stride=None, pretrained_backbone=True):
    """Constructs a SegFormer-B1 model.

    Args:
        num_classes (int): number of classes.
        output_stride (int): not used for SegFormer, kept for API compatibility.
        pretrained_backbone (bool): If True, use the pretrained backbone.
    """
    from .segformer import segformer_b1 as segformer_b1_func
    return segformer_b1_func(num_classes=num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)

def segformer_b2(num_classes=21, output_stride=None, pretrained_backbone=True):
    """Constructs a SegFormer-B2 model.

    Args:
        num_classes (int): number of classes.
        output_stride (int): not used for SegFormer, kept for API compatibility.
        pretrained_backbone (bool): If True, use the pretrained backbone.
    """
    from .segformer import segformer_b2 as segformer_b2_func
    return segformer_b2_func(num_classes=num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)

def segformer_b3(num_classes=21, output_stride=None, pretrained_backbone=True):
    """Constructs a SegFormer-B3 model.

    Args:
        num_classes (int): number of classes.
        output_stride (int): not used for SegFormer, kept for API compatibility.
        pretrained_backbone (bool): If True, use the pretrained backbone.
    """
    from .segformer import segformer_b3 as segformer_b3_func
    return segformer_b3_func(num_classes=num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)

def segformer_b4(num_classes=21, output_stride=None, pretrained_backbone=True):
    """Constructs a SegFormer-B4 model.

    Args:
        num_classes (int): number of classes.
        output_stride (int): not used for SegFormer, kept for API compatibility.
        pretrained_backbone (bool): If True, use the pretrained backbone.
    """
    from .segformer import segformer_b4 as segformer_b4_func
    return segformer_b4_func(num_classes=num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)

def segformer_b5(num_classes=21, output_stride=None, pretrained_backbone=True):
    """Constructs a SegFormer-B5 model.

    Args:
        num_classes (int): number of classes.
        output_stride (int): not used for SegFormer, kept for API compatibility.
        pretrained_backbone (bool): If True, use the pretrained backbone.
    """
    from .segformer import segformer_b5 as segformer_b5_func
    return segformer_b5_func(num_classes=num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)


# Mask2Former models
def mask2former_swin_tiny(num_classes=21, output_stride=None, pretrained_backbone=True):
    """Constructs a Mask2Former model with Swin-Tiny backbone.

    Args:
        num_classes (int): number of classes.
        output_stride (int): not used for Mask2Former, kept for API compatibility.
        pretrained_backbone (bool): If True, use the pretrained backbone.
    """
    from .mask2former import mask2former_swin_tiny as mask2former_swin_tiny_func
    return mask2former_swin_tiny_func(num_classes=num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)

def mask2former_swin_large_cityscapes(num_classes=21, output_stride=None, pretrained_backbone=True):
    """Constructs a Mask2Former model with Swin-Large backbone (Cityscapes pretrained).

    Args:
        num_classes (int): number of classes.
        output_stride (int): not used for Mask2Former, kept for API compatibility.
        pretrained_backbone (bool): If True, use the pretrained backbone.
    """
    from .mask2former import mask2former_swin_large_cityscapes as mask2former_swin_large_cityscapes_func
    return mask2former_swin_large_cityscapes_func(num_classes=num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)

def mask2former_swin_large_ade(num_classes=21, output_stride=None, pretrained_backbone=True):
    """Constructs a Mask2Former model with Swin-Large backbone (ADE20K pretrained).

    Args:
        num_classes (int): number of classes.
        output_stride (int): not used for Mask2Former, kept for API compatibility.
        pretrained_backbone (bool): If True, use the pretrained backbone.
    """
    from .mask2former import mask2former_swin_large_ade as mask2former_swin_large_ade_func
    return mask2former_swin_large_ade_func(num_classes=num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)

def mask2former_swin_base_coco(num_classes=21, output_stride=None, pretrained_backbone=True):
    """Constructs a Mask2Former model with Swin-Base backbone (COCO pretrained).

    Args:
        num_classes (int): number of classes.
        output_stride (int): not used for Mask2Former, kept for API compatibility.
        pretrained_backbone (bool): If True, use the pretrained backbone.
    """
    from .mask2former import mask2former_swin_base_coco as mask2former_swin_base_coco_func
    return mask2former_swin_base_coco_func(num_classes=num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)

def mask2former_swin_base_ade(num_classes=21, output_stride=None, pretrained_backbone=True):
    """Constructs a Mask2Former model with Swin-Base backbone (ADE20K pretrained).

    Args:
        num_classes (int): number of classes.
        output_stride (int): not used for Mask2Former, kept for API compatibility.
        pretrained_backbone (bool): If True, use the pretrained backbone.
    """
    from .mask2former import mask2former_swin_base_ade as mask2former_swin_base_ade_func
    return mask2former_swin_base_ade_func(num_classes=num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)

def mask2former_swin_tiny_ade(num_classes=21, output_stride=None, pretrained_backbone=True):
    """Constructs a Mask2Former model with Swin-Tiny backbone (ADE20K pretrained).

    Args:
        num_classes (int): number of classes.
        output_stride (int): not used for Mask2Former, kept for API compatibility.
        pretrained_backbone (bool): If True, use the pretrained backbone.
    """
    from .mask2former import mask2former_swin_tiny_ade as mask2former_swin_tiny_ade_func
    return mask2former_swin_tiny_ade_func(num_classes=num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)

def mask2former_swin_small_ade(num_classes=21, output_stride=None, pretrained_backbone=True):
    """Constructs a Mask2Former model with Swin-Small backbone (ADE20K pretrained).

    Args:
        num_classes (int): number of classes.
        output_stride (int): not used for Mask2Former, kept for API compatibility.
        pretrained_backbone (bool): If True, use the pretrained backbone.
    """
    from .mask2former import mask2former_swin_small_ade as mask2former_swin_small_ade_func
    return mask2former_swin_small_ade_func(num_classes=num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)


# UPerNet models
def upernet_swin_tiny(num_classes=21, output_stride=None, pretrained_backbone=True):
    """Constructs a UPerNet model with Swin-Tiny backbone.

    Args:
        num_classes (int): number of classes.
        output_stride (int): not used for UPerNet, kept for API compatibility.
        pretrained_backbone (bool): If True, use the pretrained backbone.
    """
    from .upernet import upernet_swin_tiny as upernet_swin_tiny_func
    return upernet_swin_tiny_func(num_classes=num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)

# MobileViTV2 models
def mobilevitv2_1_0(num_classes=21, output_stride=None, pretrained_backbone=True):
    """Constructs a MobileViTV2-1.0 model.

    Args:
        num_classes (int): number of classes.
        output_stride (int): not used for MobileViTV2, kept for API compatibility.
        pretrained_backbone (bool): If True, use the pretrained backbone.
    """
    from .mobilevitv2 import mobilevitv2_1_0 as mobilevitv2_1_0_func
    return mobilevitv2_1_0_func(num_classes=num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)

def mobilevitv2_0_75(num_classes=21, output_stride=None, pretrained_backbone=True):
    """Constructs a MobileViTV2-0.75 model (lighter version).

    Args:
        num_classes (int): number of classes.
        output_stride (int): not used for MobileViTV2, kept for API compatibility.
        pretrained_backbone (bool): If True, use the pretrained backbone.
    """
    from .mobilevitv2 import mobilevitv2_0_75 as mobilevitv2_0_75_func
    return mobilevitv2_0_75_func(num_classes=num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)

def mobilevitv2_0_5(num_classes=21, output_stride=None, pretrained_backbone=True):
    """Constructs a MobileViTV2-0.5 model (lightest version).

    Args:
        num_classes (int): number of classes.
        output_stride (int): not used for MobileViTV2, kept for API compatibility.
        pretrained_backbone (bool): If True, use the pretrained backbone.
    """
    from .mobilevitv2 import mobilevitv2_0_5 as mobilevitv2_0_5_func
    return mobilevitv2_0_5_func(num_classes=num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)

def upernet_swin_small(num_classes=21, output_stride=None, pretrained_backbone=True):
    """Constructs a UPerNet model with Swin-Small backbone.

    Args:
        num_classes (int): number of classes.
        output_stride (int): not used for UPerNet, kept for API compatibility.
        pretrained_backbone (bool): If True, use the pretrained backbone.
    """
    from .upernet import upernet_swin_small as upernet_swin_small_func
    return upernet_swin_small_func(num_classes=num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)

def upernet_swin_base(num_classes=21, output_stride=None, pretrained_backbone=True):
    """Constructs a UPerNet model with Swin-Base backbone.

    Args:
        num_classes (int): number of classes.
        output_stride (int): not used for UPerNet, kept for API compatibility.
        pretrained_backbone (bool): If True, use the pretrained backbone.
    """
    from .upernet import upernet_swin_base as upernet_swin_base_func
    return upernet_swin_base_func(num_classes=num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)

def upernet_convnext_tiny(num_classes=21, output_stride=None, pretrained_backbone=True):
    """Constructs a UPerNet model with ConvNeXt-Tiny backbone.

    Args:
        num_classes (int): number of classes.
        output_stride (int): not used for UPerNet, kept for API compatibility.
        pretrained_backbone (bool): If True, use the pretrained backbone.
    """
    from .upernet import upernet_convnext_tiny as upernet_convnext_tiny_func
    return upernet_convnext_tiny_func(num_classes=num_classes, output_stride=output_stride, pretrained_backbone=pretrained_backbone)